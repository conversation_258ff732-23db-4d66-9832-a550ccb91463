// Please don't change the pre-written code
// Import the necessary modules here
import nodemailer from "nodemailer";
import readline from "readline";

const Solution = () => {
  // Write your code here
  const readlinee = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  readlinee.question("Enter your mail: ", (mail) => {
    try {
      const transporter = nodemailer.createTransport({
        service: "gmail",
        auth: {
          user: "<EMAIL>",
          pass: "slwvvlczduktvhdj",
        },
      });

      const options = {
        from: "<EMAIL>",
        to: mail,
        subject: "Coding Ninjas",
        text: "The world has enough coders; be a coding ninja!",
      };
      transporter.sendMail(options, (err, info) => {
        if (err) {
          console.log("Error occurred: ", err);
        } else {
          console.log("Email sent ", mail);
        }
        readlinee.close();
      });
    } catch (error) {
      console.log(error);
    }
    
  });
};
Solution();

export default Solution;
